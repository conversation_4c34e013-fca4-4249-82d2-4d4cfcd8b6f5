# 🌟 Cloudinary Integration for Donation Photos & Receipts

## ✅ What's Been Implemented

Your donation system now uses **Cloudinary** for storing photos and receipts when organizations update donation status, instead of local file storage.

### 🔧 Changes Made:

1. **New Cloudinary Upload Middleware**:
   - `uploadDonationPhotoToCloudinary` - For donation photos
   - `uploadReceiptToCloudinary` - For receipts (supports images and PDFs)

2. **Updated Routes**:
   - `/:donationId/received` - Now uses Cloudinary for photos
   - `/:donationId/confirmed` - Now uses Cloudinary for receipts

3. **Enhanced Controllers**:
   - `markDonationAsReceived` - Stores Cloudinary URLs
   - `markDonationAsConfirmed` - Stores Cloudinary URLs

4. **Frontend Compatibility**:
   - Handles both old local URLs and new Cloudinary URLs
   - Automatic URL detection (http vs local path)

## 🔑 Required Environment Variables

Add these to your `.env` file:

```env
# Cloudinary Configuration (Backend)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Cloudinary Configuration (Frontend - Optional)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=charity
```

## 📁 Cloudinary Folder Structure

Files will be organized in Cloudinary as:
```
charity-donation/
├── donation-photos/     # Photos when marking as received
├── receipts/            # Receipt files when marking as confirmed
├── causes/              # Cause images (existing)
├── campaigns/           # Campaign images (existing)
└── organizations/       # Organization logos (existing)
```

## 🚀 How It Works

### When Organization Marks Donation as "Received":
1. Photo is uploaded to Cloudinary (`donation-photos` folder)
2. Cloudinary URL is stored in `donation.receiptImage`
3. Metadata includes Cloudinary public_id for future management

### When Organization Marks Donation as "Confirmed":
1. Receipt file is uploaded to Cloudinary (`receipts` folder)
2. Cloudinary URL is stored in `donation.receiptImage`
3. PDF receipts are also supported
4. Auto-generated PDF receipt is still stored locally

## 🔄 Backward Compatibility

The system supports both:
- **New donations**: Cloudinary URLs (start with `http`)
- **Old donations**: Local file paths (start with `/uploads/`)

Frontend automatically detects and handles both URL types.

## 🎯 Benefits

✅ **Reliable Storage**: No more local file storage issues
✅ **Automatic Optimization**: Images are optimized for web
✅ **CDN Delivery**: Fast global image delivery
✅ **PDF Support**: Handles both images and PDF receipts
✅ **Scalable**: No server storage limitations
✅ **Backup**: Cloudinary provides reliable backup

## 🧪 Testing

1. Set up your Cloudinary account and add environment variables
2. Test donation status updates:
   - Mark donation as "Received" with photo
   - Mark donation as "Confirmed" with receipt
3. Verify images appear correctly in donor dashboard
4. Check Cloudinary dashboard for uploaded files

## 🔧 Troubleshooting

**Images not showing?**
- Check Cloudinary environment variables
- Verify Cloudinary account is active
- Check browser console for URL errors

**Upload failing?**
- Ensure file size is under 5MB
- Check Cloudinary API limits
- Verify file format is supported

**Old donations not working?**
- System maintains backward compatibility
- Old local files should still work
- Only new uploads use Cloudinary
