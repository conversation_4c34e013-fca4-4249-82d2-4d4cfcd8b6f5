{"name": "charity-donation-backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "npx ts-node --transpile-only src/server.ts", "build": "npm install", "start": "npx ts-node --transpile-only src/server.ts", "seed:donations": "npx ts-node --transpile-only src/scripts/seedDonations.ts", "test:donation-flow": "npx ts-node --transpile-only src/scripts/testDonationFlow.ts"}, "dependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.1", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.17.46", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "@types/socket.io": "^3.0.1", "@types/stripe": "^8.0.416", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "body-parser": "^2.2.0", "cloudinary": "^2.6.1", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "firebase": "^11.6.1", "firebase-admin": "^13.3.0", "fs-extra": "^11.3.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.8.7", "morgan": "^1.10.0", "multer": "^2.0.0", "nodemailer": "^7.0.3", "pdfkit": "^0.17.1", "resend": "^4.5.1", "socket.io": "^4.8.1", "stripe": "^14.25.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}