"use client";

import { useState, useRef } from "react";
import { toast } from "react-hot-toast";
import { Camera, Upload } from "lucide-react";
import { motion } from "framer-motion";
import { useUploadCoverImageMutation } from "@/store/api/profileApi";
import { useDispatch } from "react-redux";
import { profileApi } from "@/store/api/profileApi";
import Image from "next/image";
import { getProfileImageUrl } from "@/utils/url";

interface CoverImageUploadProps {
	currentCoverImage?: string;
	onImageUpdate?: (imageUrl: string) => void;
	className?: string;
}

export default function CoverImageUpload({
	currentCoverImage,
	onImageUpdate,
	className = "",
}: CoverImageUploadProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [previewImage, setPreviewImage] = useState<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [uploadCoverImage] = useUploadCoverImageMutation();
	const dispatch = useDispatch();

	const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		// Validate file type
		if (!file.type.startsWith("image/")) {
			toast.error("Please select an image file");
			return;
		}

		// Validate file size (10MB max for cover images)
		if (file.size > 10 * 1024 * 1024) {
			toast.error("File size must be less than 10MB");
			return;
		}

		// Create preview
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreviewImage(e.target?.result as string);
		};
		reader.readAsDataURL(file);

		// Upload file
		handleUpload(file);
	};

	const handleUpload = async (file: File) => {
		setIsUploading(true);
		console.log("🚀 Starting cover image upload...");

		try {
			const formData = new FormData();
			formData.append("profileImage", file); // Using profileImage field name temporarily

			const result = await uploadCoverImage(formData).unwrap();
			console.log("📥 API Response:", result);

			if (result.coverImage) {
				// Update the image - this will replace the old one
				onImageUpdate?.(result.coverImage);
				setPreviewImage(null);

				// Invalidate profile cache to ensure fresh data on refresh
				dispatch(profileApi.util.invalidateTags(['Profile', 'OrganizationProfile']));

				console.log("✅ Cover photo updated successfully");
			}
		} catch (error) {
			console.error("❌ Cover upload failed:", error);
		} finally {
			setIsUploading(false);
		}
	};

	const handleButtonClick = (e?: React.MouseEvent) => {
		e?.preventDefault();
		e?.stopPropagation();
		console.log("Cover upload button clicked!");
		fileInputRef.current?.click();
	};



	const displayImage = previewImage || getProfileImageUrl(currentCoverImage);

	return (
		<div className={`relative w-full h-48 ${className}`}>
			<input
				ref={fileInputRef}
				type="file"
				accept="image/*"
				onChange={handleFileSelect}
				className="hidden"
			/>

			{/* Cover Image Display */}
			<div className="relative w-full h-full overflow-hidden rounded-t-lg group">
				{displayImage ? (
					<Image
						src={displayImage}
						alt="Cover"
						className="w-full h-full object-cover"
						fill
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						onError={(e) => {
							// Fallback to gradient on error
							e.currentTarget.style.display = 'none';
						}}
					/>
				) : (
					<div className="w-full h-full bg-gradient-to-r from-teal-500 to-green-400"></div>
				)}

				{/* Upload Button - Only show on hover */}
				<motion.button
					type="button"
					onClick={handleButtonClick}
					disabled={isUploading}
					className="absolute top-4 right-4 bg-black bg-opacity-60 hover:bg-opacity-80 text-white rounded-full p-3 z-20 transition-all duration-200 backdrop-blur-sm shadow-lg opacity-0 group-hover:opacity-100"
					whileHover={{ scale: 1.1 }}
					whileTap={{ scale: 0.9 }}
					title={displayImage ? "Change cover photo" : "Upload cover photo"}
					style={{ pointerEvents: 'auto', zIndex: 20 }}
				>
					{isUploading ? (
						<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
					) : (
						<Camera className="h-5 w-5" />
					)}
				</motion.button>



				{/* Upload Instructions Overlay - Only when no image */}
				{!displayImage && (
					<div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
						<div className="text-center text-white">
							<Camera className="h-8 w-8 mx-auto mb-2" />
							<p className="text-sm font-medium">Add Cover Photo</p>
							<p className="text-xs opacity-75">Hover and click camera icon</p>
						</div>
					</div>
				)}


			</div>
		</div>
	);
}
