# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/.next
/out

# environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# next.js build output
.next/
/out/

# logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# vscode
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json

# TypeScript
*.tsbuildinfo

# other
*.swp
